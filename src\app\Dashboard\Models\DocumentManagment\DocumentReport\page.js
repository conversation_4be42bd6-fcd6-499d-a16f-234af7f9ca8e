"use client";
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import Header from "@/app/Dashboard/Components/Header";
import Sidebar from "@/app/Dashboard/Components/Sidebar";
import { API_URL_DriverDocument, API_URL_VehicleDocument } from "@/app/Dashboard/Components/ApiUrl/ApiUrls";
import { isAuthenticated } from "@/utils/verifytoken";
import { Icons, toast } from "react-toastify";
import Image from "next/image";
import { getUserName } from "@/utils/storageUtils";

const DocumentReportPage = () => {
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [isVerifyModalOpen, setIsVerifyModalOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [verificationStatus, setVerificationStatus] = useState("");

  // Filter states
  const [selectedEntity, setSelectedEntity] = useState("all");
  const [selectedDocumentType, setSelectedDocumentType] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");

  // Bulk verification modal states
  const [isBulkVerifyModalOpen, setIsBulkVerifyModalOpen] = useState(false);
  const [bulkSelectedEntity, setBulkSelectedEntity] = useState("all");
  const [bulkSelectedDocumentType, setBulkSelectedDocumentType] = useState("all");
  const [bulkSelectedStatus, setBulkSelectedStatus] = useState("all");
  const [documentVerificationStates, setDocumentVerificationStates] = useState({});

  // Inline verification states for modal enhancements
  const [inlineVerificationStates, setInlineVerificationStates] = useState({}); // Track which documents are in inline verification mode
  const [inlineVerificationValues, setInlineVerificationValues] = useState({}); // Track verification values for inline editing

  // Client-side verification override state to handle backend bugs
  // This tracks user verification actions that the backend fails to persist
  const [clientVerificationOverrides, setClientVerificationOverrides] = useState({});

  // Helper function to get full document URL
  const getDocumentUrl = (documentPath) => {
    if (!documentPath) return null;
    // If it's already a full URL, return as is
    if (documentPath.startsWith('http')) return documentPath;
    // If it starts with /uploads, prepend the base URL
    if (documentPath.startsWith('/uploads')) {
      return `${window.location.origin}${documentPath}`;
    }
    // Otherwise, assume it's a relative path
    return `${window.location.origin}/uploads/${documentPath}`;
  };

  // Helper function to get vehicle/driver display name
  const getEntityDisplayName = (document) => {
    if (!document) return 'Information Not Available';

    // If it's a driver document and we have driver info
    if (document.Driverid) {
      const firstName = document.Driverid.firstName || '';
      const lastName = document.Driverid.lastName || '';
      if (firstName || lastName) {
        return `Driver: ${firstName} ${lastName}`.trim();
      }
      return 'Driver: Information Not Available';
    }

    // If it's a vehicle document and we have vehicle info
    if (document.Vehicleid) {
      const manufacturer = document.Vehicleid.manufacturer || '';
      const model = document.Vehicleid.model || '';
      const registrationNumber = document.Vehicleid.registrationNumber || '';

      if (manufacturer && model) {
        return `Vehicle: ${manufacturer} ${model}${registrationNumber ? ` (${registrationNumber})` : ''}`;
      } else if (registrationNumber) {
        return `Vehicle: ${registrationNumber}`;
      }
      return 'Vehicle: Information Not Available';
    }

    return 'Entity Information Not Available';
  };

  // Helper function to format date safely
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleDateString();
    } catch (error) {
      return 'Invalid Date';
    }
  };

  // Helper function to check if a document was created today
  const isDocumentCreatedToday = (document) => {
    if (!document.createdAt) return false;

    const today = new Date();
    const createdDate = new Date(document.createdAt);

    return today.getFullYear() === createdDate.getFullYear() &&
           today.getMonth() === createdDate.getMonth() &&
           today.getDate() === createdDate.getDate();
  };

  // Helper function to get document status based on isActive field with fallback to verificationStatus
  const getDocumentStatus = (document) => {
    // CRITICAL WORKAROUND FOR DOUBLE BACKEND BUG:
    // Both Driver Documents and Vehicle Documents APIs have TWO bugs:
    // 1. isActive field: API receives false but returns true
    // 2. verificationStatus field: API receives "rejected" but returns undefined
    // This affects both /api/DriverDocuments/{id} and /api/VehicleDocuments/{id} endpoints

    // Check client-side override first (user's most recent verification action)
    if (clientVerificationOverrides[document._id]) {
      return clientVerificationOverrides[document._id];
    }

    // Since backend doesn't update verificationStatus correctly either, we need to track
    // the user's intended verification action in the UI state and use that for display

    // First check if verificationStatus is explicitly "rejected"
    if (document.verificationStatus === 'rejected') {
      return 'rejected';
    }

    // Check if isActive field exists and use it as primary indicator
    // This applies to both Driver Documents and Vehicle Documents API responses
    if (document.isActive !== undefined && document.isActive !== null) {
      // If isActive: true → Display as "Verified"
      // If isActive: false → Display as "Not Verified" (mapped to "rejected" for consistency)
      return document.isActive ? 'verified' : 'rejected';
    }

    // Fallback to existing verificationStatus field for backward compatibility
    // This handles documents that don't have the isActive field
    return document.verificationStatus || 'pending';
  };

  // Helper function to get display status with date-based "New" status for modals
  const getDisplayStatus = (document, isInModal = false) => {
    // In modal context, check if document was created today and show as "New"
    if (isInModal && isDocumentCreatedToday(document)) {
      return 'new';
    }

    // Otherwise use the standard status logic
    return getDocumentStatus(document);
  };



  // Helper function to get unique document types from the data
  const getUniqueDocumentTypes = () => {
    const types = new Set();
    documents.forEach(doc => {
      if (doc.documentname) {
        types.add(doc.documentname.toLowerCase());
      }
    });
    return Array.from(types);
  };

  // Helper function to count active filters
  const getActiveFiltersCount = () => {
    let count = 0;
    if (selectedEntity !== "all") count++;
    if (selectedDocumentType !== "all") count++;
    if (selectedStatus !== "all") count++;
    if (searchTerm) count++;
    return count;
  };

  // Filter documents for bulk verification modal
  const getBulkFilteredDocuments = () => {
    return documents.filter((doc) => {
      // Entity filter
      const docEntity = doc.documentType === 'driver' || doc.Driverid ? 'driver' : 'vehicle';
      const matchesEntity = bulkSelectedEntity === 'all' || docEntity === bulkSelectedEntity;

      // Document Type filter
      const documentName = doc.documentname || '';
      const matchesDocumentType = bulkSelectedDocumentType === 'all' ||
        documentName.toLowerCase().includes(bulkSelectedDocumentType.toLowerCase());

      // Status filter
      const docStatus = getDocumentStatus(doc);
      const matchesStatus = bulkSelectedStatus === 'all' || docStatus === bulkSelectedStatus;

      return matchesEntity && matchesDocumentType && matchesStatus;
    });
  };

  // Fetch documents data
  const fetchDocuments = async () => {
    try {
      if (!isAuthenticated()) {
        toast.error("Please login to access this page");
        return;
      }

      // Fetch both driver and vehicle documents in parallel
      const [driverResponse, vehicleResponse] = await Promise.all([
        axios.get(API_URL_DriverDocument),
        axios.get(API_URL_VehicleDocument)
      ]);

      const driverDocuments = driverResponse.data?.result || [];
      const vehicleDocuments = vehicleResponse.data?.documents || [];

      // Documents fetched successfully

      // Combine both arrays and add a source identifier
      const allDocuments = [
        ...driverDocuments.map(doc => ({ ...doc, documentType: 'driver' })),
        ...vehicleDocuments.map(doc => ({ ...doc, documentType: 'vehicle' }))
      ];

      // Documents combined and ready for display

      setDocuments(allDocuments);
    } catch (error) {
      console.error("Error fetching documents:", error);
      toast.error("Failed to fetch document reports");
      setDocuments([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDocuments();
  }, []);

  // Filter documents based on search term and filter criteria
  const filteredDocuments = documents.filter((doc) => {
    const searchLower = searchTerm.toLowerCase();

    // Driver document fields
    const firstName = doc.Driverid?.firstName || '';
    const lastName = doc.Driverid?.lastName || '';
    const licenseNumber = doc.Driverid?.licenseNumber || '';
    const email = doc.Driverid?.email || '';

    // Vehicle document fields
    const manufacturer = doc.Vehicleid?.manufacturer || '';
    const model = doc.Vehicleid?.model || '';
    const registrationNumber = doc.Vehicleid?.registrationNumber || '';
    const vehicleType = doc.Vehicleid?.type || '';

    // Common fields
    const documentName = doc.documentname || '';
    const adminCompanyName = doc.adminCompanyName || '';

    // Search filter
    const matchesSearch = searchTerm === '' || (
      firstName.toLowerCase().includes(searchLower) ||
      lastName.toLowerCase().includes(searchLower) ||
      licenseNumber.toLowerCase().includes(searchLower) ||
      email.toLowerCase().includes(searchLower) ||
      manufacturer.toLowerCase().includes(searchLower) ||
      model.toLowerCase().includes(searchLower) ||
      registrationNumber.toLowerCase().includes(searchLower) ||
      vehicleType.toLowerCase().includes(searchLower) ||
      documentName.toLowerCase().includes(searchLower) ||
      adminCompanyName.toLowerCase().includes(searchLower)
    );

    // Entity filter
    const docEntity = doc.documentType === 'driver' || doc.Driverid ? 'driver' : 'vehicle';
    const matchesEntity = selectedEntity === 'all' || docEntity === selectedEntity;

    // Document Type filter
    const matchesDocumentType = selectedDocumentType === 'all' ||
      documentName.toLowerCase().includes(selectedDocumentType.toLowerCase());

    // Status filter
    const docStatus = getDocumentStatus(doc);
    const matchesStatus = selectedStatus === 'all' || docStatus === selectedStatus;

    return matchesSearch && matchesEntity && matchesDocumentType && matchesStatus;
  });



  // Action handlers
  const handleView = (documentId) => {
    // Implement view functionality
    console.log("View document:", documentId);
    toast.info("View functionality to be implemented");
  };

  const handleVerify = (documentId) => {
    // Find the document and open modal
    const document = documents.find(doc => doc._id === documentId);
    if (document) {
      setSelectedDocument(document);
      // Pre-select radio button based on isActive field with fallback to verificationStatus
      // This implements the two-way binding: isActive -> UI pre-selection
      setVerificationStatus(getDocumentStatus(document));
      setIsVerifyModalOpen(true);
    }
  };

  const handleVerifySubmit = async () => {
    // Enhanced verification with client-side override for backend bug workaround

    if (!selectedDocument || !verificationStatus) {
      toast.error("Please select a verification status");
      return;
    }

    try {
      if (!isAuthenticated()) {
        toast.error("Please login to access this page");
        return;
      }

      const token = localStorage.getItem("token");
      const verifiedBy = getUserName() || "Unknown User";

      // Create update data with both isActive and verificationStatus for two-way binding
      const updateData = {
        verificationStatus,
        verifiedBy,
        // Set isActive based on verification status selection
        isActive: verificationStatus === 'verified' ? true : verificationStatus === 'rejected' ? false : undefined
      };

      // Process update data for API request

      // BACKEND BUG IDENTIFIED: The API receives isActive: false correctly but doesn't update it in the database
      // The frontend correctly sends isActive: false for rejected documents, but the backend keeps isActive: true
      // This is a backend issue that needs to be fixed in the API endpoint

      // IMPORTANT: Do NOT remove isActive when it's false, only when it's undefined
      if (updateData.isActive === undefined) {
        delete updateData.isActive;
      } else {
        // Ensure boolean values are properly sent
        updateData.isActive = Boolean(updateData.isActive);
      }

      // Determine which API endpoint to use based on document type
      const apiUrl = selectedDocument.documentType === 'driver' || selectedDocument.Driverid
        ? `${API_URL_DriverDocument}/${selectedDocument._id}`
        : `${API_URL_VehicleDocument}/${selectedDocument._id}`;

      const response = await axios.put(
        apiUrl,
        updateData,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data && response.data.message) {
        // WORKAROUND: Set client-side override since backend doesn't update correctly
        setClientVerificationOverrides(prev => ({
          ...prev,
          [selectedDocument._id]: verificationStatus
        }));

        // Client-side override set to handle backend bug

        toast.success(`Document ${verificationStatus} successfully!`);
        setIsVerifyModalOpen(false);
        setSelectedDocument(null);
        setVerificationStatus("");

        // Refresh the documents list to show updated status
        fetchDocuments();
      } else {
        toast.error("Failed to update document verification status");
      }
    } catch (error) {
      console.error("Error verifying document:", error);
      if (error.response?.data?.error) {
        toast.error(error.response.data.error);
      } else {
        toast.error("Failed to verify document. Please try again.");
      }
    }
  };

  const closeVerifyModal = () => {
    setIsVerifyModalOpen(false);
    setSelectedDocument(null);
    setVerificationStatus("");
  };

  const handleVerifyDocument = () => {
    // Initialize verification states for all documents in bulk modal
    // Pre-select radio buttons based on isActive field with fallback to verificationStatus
    // This implements the two-way binding: isActive -> UI pre-selection for bulk operations
    const initialStates = {};
    documents.forEach(doc => {
      initialStates[doc._id] = getDocumentStatus(doc);
    });
    setDocumentVerificationStates(initialStates);
    setIsBulkVerifyModalOpen(true);
  };

  const handleAddDocumentType = () => {
    toast.info("Add Document Type functionality to be implemented");
  };

  // Handle individual document verification status change in bulk modal
  const handleDocumentVerificationChange = (documentId, status) => {
    setDocumentVerificationStates(prev => ({
      ...prev,
      [documentId]: status
    }));
  };

  // Handle inline verification mode toggle
  const handleInlineVerifyToggle = (documentId) => {
    setInlineVerificationStates(prev => ({
      ...prev,
      [documentId]: !prev[documentId]
    }));

    // Initialize verification value if entering inline mode
    if (!inlineVerificationStates[documentId]) {
      const document = documents.find(doc => doc._id === documentId);
      setInlineVerificationValues(prev => ({
        ...prev,
        [documentId]: getDocumentStatus(document)
      }));
    }
  };

  // Handle inline verification value change
  const handleInlineVerificationChange = (documentId, value) => {
    // Handle inline verification radio button change

    setInlineVerificationValues(prev => ({
      ...prev,
      [documentId]: value
    }));
  };

  // Handle inline verification submit
  const handleInlineVerificationSubmit = async (documentId) => {
    const document = documents.find(doc => doc._id === documentId);
    const newStatus = inlineVerificationValues[documentId];

    if (!document || !newStatus) {
      toast.error("Please select a verification status");
      return;
    }

    try {
      if (!isAuthenticated()) {
        toast.error("Please login to access this page");
        return;
      }

      const token = localStorage.getItem("token");
      const verifiedBy = getUserName() || "Unknown User";

      // Create update data with both isActive and verificationStatus for two-way binding
      const updateData = {
        verificationStatus: newStatus,
        verifiedBy,
        // Set isActive based on verification status selection
        isActive: newStatus === 'verified' ? true : newStatus === 'rejected' ? false : undefined
      };

      // BACKEND BUG: Same issue as individual verification - API doesn't update isActive field properly

      // IMPORTANT: Do NOT remove isActive when it's false, only when it's undefined
      if (updateData.isActive === undefined) {
        delete updateData.isActive;
      } else {
        // Ensure boolean values are properly sent
        updateData.isActive = Boolean(updateData.isActive);
      }

      // Determine which API endpoint to use based on document type
      const apiUrl = document.documentType === 'driver' || document.Driverid
        ? `${API_URL_DriverDocument}/${document._id}`
        : `${API_URL_VehicleDocument}/${document._id}`;

      const response = await axios.put(
        apiUrl,
        updateData,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data && response.data.message) {
        // WORKAROUND: Set client-side override since backend doesn't update correctly
        setClientVerificationOverrides(prev => ({
          ...prev,
          [documentId]: newStatus
        }));

        // Client-side override set to handle backend bug

        toast.success(`Document ${newStatus} successfully!`);

        // Exit inline verification mode
        setInlineVerificationStates(prev => ({
          ...prev,
          [documentId]: false
        }));

        // Clear inline verification value
        setInlineVerificationValues(prev => {
          const newValues = { ...prev };
          delete newValues[documentId];
          return newValues;
        });

        // Refresh the documents list to show updated status
        fetchDocuments();
      } else {
        toast.error("Failed to update document verification status");
      }
    } catch (error) {
      console.error("Error verifying document:", error);
      if (error.response?.data?.error) {
        toast.error(error.response.data.error);
      } else {
        toast.error("Failed to verify document. Please try again.");
      }
    }
  };

  // Handle inline verification cancel
  const handleInlineVerificationCancel = (documentId) => {
    setInlineVerificationStates(prev => ({
      ...prev,
      [documentId]: false
    }));

    // Clear inline verification value
    setInlineVerificationValues(prev => {
      const newValues = { ...prev };
      delete newValues[documentId];
      return newValues;
    });
  };

  // Handle bulk verification submit
  const handleBulkVerificationSubmit = async () => {
    try {
      if (!isAuthenticated()) {
        toast.error("Please login to access this page");
        return;
      }

      const token = localStorage.getItem("token");
      const verifiedBy = getUserName() || "Unknown User";

      // Get documents that have changed status
      const changedDocuments = [];
      Object.keys(documentVerificationStates).forEach(docId => {
        const doc = documents.find(d => d._id === docId);
        const newStatus = documentVerificationStates[docId];
        const currentStatus = getDocumentStatus(doc);

        if (newStatus !== currentStatus) {
          changedDocuments.push({
            document: doc,
            newStatus: newStatus
          });
        }
      });

      if (changedDocuments.length === 0) {
        toast.info("No changes to save");
        return;
      }

      // Update each changed document
      const updatePromises = changedDocuments.map(({ document, newStatus }) => {
        const apiUrl = document.documentType === 'driver' || document.Driverid
          ? `${API_URL_DriverDocument}/${document._id}`
          : `${API_URL_VehicleDocument}/${document._id}`;

        // Create update data with both isActive and verificationStatus for two-way binding
        const updateData = {
          verificationStatus: newStatus,
          verifiedBy,
          // Set isActive based on verification status selection
          isActive: newStatus === 'verified' ? true : newStatus === 'rejected' ? false : undefined
        };

        // BACKEND BUG: Same issue applies to bulk verification

        // IMPORTANT: Do NOT remove isActive when it's false, only when it's undefined
        if (updateData.isActive === undefined) {
          delete updateData.isActive;
        } else {
          // Ensure boolean values are properly sent
          updateData.isActive = Boolean(updateData.isActive);
        }

        return axios.put(
          apiUrl,
          updateData,
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${token}`,
            },
          }
        );
      });

      await Promise.all(updatePromises);

      // WORKAROUND: Set client-side overrides for all changed documents since backend doesn't update correctly
      const overrides = {};
      changedDocuments.forEach(({ document, newStatus }) => {
        overrides[document._id] = newStatus;
      });

      setClientVerificationOverrides(prev => ({
        ...prev,
        ...overrides
      }));

      // Client-side overrides set to handle backend bug

      toast.success(`Successfully updated ${changedDocuments.length} document${changedDocuments.length > 1 ? 's' : ''}!`);
      setIsBulkVerifyModalOpen(false);
      setDocumentVerificationStates({});

      // Refresh the documents list
      fetchDocuments();
    } catch (error) {
      console.error("Error in bulk verification:", error);
      toast.error("Failed to update documents. Please try again.");
    }
  };

  // Close bulk verification modal
  const closeBulkVerifyModal = () => {
    setIsBulkVerifyModalOpen(false);
    setDocumentVerificationStates({});
    setBulkSelectedEntity("all");
    setBulkSelectedDocumentType("all");
    setBulkSelectedStatus("all");
    // Clear inline verification states
    setInlineVerificationStates({});
    setInlineVerificationValues({});
    // Note: We don't clear clientVerificationOverrides here as they should persist
    // to show the correct status even after modal closes
  };

  return (
    <div className="h-[100vh] overflow-hidden">
      <Header className="min-w-full" />
      <div className="flex gap-4">
        <Sidebar />
        <div
          className="w-[80%] xl:w-[85%] h-screen flex flex-col justify-start overflow-y-auto pr-4"
          style={{
            height: "calc(100vh - 90px)",
          }}
        >
          <h1 className="text-[#313342] font-medium text-2xl py-5 pb-8 flex gap-2 items-center">
            <div className="myborder flex gap-3 border-2 border-t-0 border-l-0 border-r-0">
              <span className="opacity-65">Reports</span>
              <div className="flex items-center gap-3 myborder2">
                <span>
                  <svg
                    width="8"
                    height="16"
                    viewBox="0 0 8 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-2 h-4 object-cover object-center"
                  >
                    <path
                      d="M0.5 1L7.5 8L0.5 15"
                      stroke="#313342"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </span>
                <span>Document Reports</span>
              </div>
            </div>
          </h1>

          {/* Search Bar and Action Buttons */}
          <div className="flex justify-between items-center mb-6">
            {/* Search Bar - Left Side */}
            <div className="relative">
              <svg
                className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
              <input
                type="text"
                placeholder="Search documents..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8 pr-4 py-2 w-64 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Action Buttons - Right Side */}
            <div className="flex items-center gap-3">
              <button
                onClick={handleVerifyDocument}
                className="px-4 py-2 text-white bg-[#38384A] rounded-md hover:text-white bg-[#38384A] transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 font-medium"
              >
                Verify Document
              </button>
              <button
                onClick={handleAddDocumentType}
                className="px-4 py-2 text-white bg-[#38384A] rounded-md text-white bg-[#38384A] transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 font-medium"
              >
                Add Document Type
              </button>
            </div>
          </div>

          {/* Filter Buttons */}
          <div className="bg-gray-50 p-4 rounded-lg mb-6">
            <div className="flex flex-wrap gap-4">
              {/* Entity Filter */}
              <div className="flex flex-col">
                <label className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  Select Entity
                </label>
                <select
                  value={selectedEntity}
                  onChange={(e) => setSelectedEntity(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white min-w-[140px] shadow-sm"
                >
                  <option value="all">All Entities</option>
                  <option value="driver">Driver</option>
                  <option value="vehicle">Vehicle</option>
                </select>
              </div>

              {/* Document Type Filter */}
              <div className="flex flex-col">
                <label className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Document Type
                </label>
                <select
                  value={selectedDocumentType}
                  onChange={(e) => setSelectedDocumentType(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white min-w-[140px] shadow-sm"
                >
                  <option value="all">All Types</option>
                  {getUniqueDocumentTypes().map(type => (
                    <option key={type} value={type}>
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </option>
                  ))}
                </select>
              </div>

              {/* Status Filter */}
              <div className="flex flex-col">
                <label className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Status
                </label>
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white min-w-[140px] shadow-sm"
                >
                  <option value="all">All Status</option>
                  <option value="pending">Pending</option>
                  <option value="verified">Verified</option>
                  <option value="rejected">Rejected</option>
                </select>
              </div>

              {/* Clear Filters Button */}
              <div className="flex flex-col justify-end">
                <button
                  onClick={() => {
                    setSelectedEntity("all");
                    setSelectedDocumentType("all");
                    setSelectedStatus("all");
                    setSearchTerm("");
                  }}
                  className={`px-4 py-2 rounded-md transition-colors focus:outline-none focus:ring-2 font-medium shadow-sm ${
                    getActiveFiltersCount() > 0
                      ? 'text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
                      : 'text-gray-600 bg-gray-100 hover:bg-gray-200 focus:ring-gray-500'
                  }`}
                  disabled={getActiveFiltersCount() === 0}
                >
                  Clear Filters {getActiveFiltersCount() > 0 && `(${getActiveFiltersCount()})`}
                </button>
              </div>
            </div>
          </div>

          {/* Results Counter */}
          <div className="flex justify-between items-center mb-4">
            <div className="text-sm text-gray-600">
              Showing {filteredDocuments.length} of {documents.length} documents
            </div>
            {getActiveFiltersCount() > 0 && (
              <div className="flex items-center text-sm text-blue-600">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z" />
                </svg>
                {getActiveFiltersCount()} filter{getActiveFiltersCount() > 1 ? 's' : ''} applied
              </div>
            )}
          </div>

          {/* Table */}
          <div className="py-5">
            <div className="drop-shadow-custom4">
              <div className="overflow-x-auto custom-scrollbar">
                <table className="w-full bg-white border table-auto">
                  <thead className="font-sans font-bold text-sm text-center">
                    <tr className="text-white bg-[#38384A]">
                      <th className="py-3 px-4 min-w-[120px] w-[120px] text-center text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Entity
                      </th>
                      <th className="py-3 px-4 min-w-[200px] w-[200px] text-center text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Name
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        MOT
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Registration
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Insurance
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        License
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Maintenance Log
                      </th>
                      <th className="py-3 px-4 min-w-[120px] w-[120px] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Status
                      </th>
                      <th className="py-3 px-4 min-w-[120px] w-[120px] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="font-sans font-medium text-sm">
                    {loading ? (
                      <tr>
                        <td colSpan="9" className="py-8 text-center text-gray-500">
                          <div className="flex justify-center items-center">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                            <span className="ml-2">Loading...</span>
                          </div>
                        </td>
                      </tr>
                    ) : filteredDocuments.length === 0 ? (
                      <tr>
                        <td colSpan="9" className="py-8 text-center text-gray-500">
                          {searchTerm ? `No documents found matching "${searchTerm}"` : "No documents found"}
                        </td>
                      </tr>
                    ) : (
                      filteredDocuments.map((doc, index) => (
                        <tr key={doc._id || index} className="border-b text-center">
                          <td className="py-3 px-4 min-w-[120px] w-[120px] text-center whitespace-normal break-all overflow-hidden">
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              doc.documentType === 'driver' || doc.Driverid
                                ? 'bg-blue-100 text-blue-800'
                                : 'bg-green-100 text-green-800'
                            }`}>
                              {doc.documentType === 'driver' || doc.Driverid ? 'Driver' : 'Vehicle'}
                            </span>
                          </td>
                          <td className="py-3 px-4 min-w-[200px] w-[200px] text-center whitespace-normal break-all overflow-hidden">
                            {doc.Driverid && (doc.Driverid.firstName || doc.Driverid.lastName)
                              ? `${doc.Driverid.firstName || ''} ${doc.Driverid.lastName || ''}`.trim()
                              : doc.Vehicleid && (doc.Vehicleid.manufacturer || doc.Vehicleid.model)
                              ? `${doc.Vehicleid.manufacturer || ''} ${doc.Vehicleid.model || ''}`.trim()
                              : doc.Vehicleid?.registrationNumber || 'N/A'
                            }
                          </td>
                          <td className="py-3 px-4 min-w-[150px] w-[150px] text-center whitespace-normal break-all overflow-hidden">
                            {doc.Driverid?.taxiBadgeDate
                              ? new Date(doc.Driverid.taxiBadgeDate).toLocaleDateString()
                              : doc.Vehicleid?.motDueDate
                              ? new Date(doc.Vehicleid.motDueDate).toLocaleDateString()
                              : 'N/A'
                            }
                          </td>
                          <td className="py-3 px-4 min-w-[150px] w-[150px] text-center whitespace-normal break-all overflow-hidden">
                            {doc.Driverid?.licenseNumber || doc.Vehicleid?.registrationNumber || 'N/A'}
                          </td>
                          <td className="py-3 px-4 min-w-[150px] w-[150px] text-center whitespace-normal break-all overflow-hidden">
                            {doc.Driverid?.insurance || doc.Vehicleid?.Insurance || 'N/A'}
                          </td>
                          <td className="py-3 px-4 min-w-[150px] w-[150px] text-center whitespace-normal break-all overflow-hidden">
                            {doc.Driverid?.licenseExpiryDate
                              ? new Date(doc.Driverid.licenseExpiryDate).toLocaleDateString()
                              : doc.Vehicleid?.PlateExpiryDate
                              ? new Date(doc.Vehicleid.PlateExpiryDate).toLocaleDateString()
                              : 'N/A'
                            }
                          </td>
                          <td className="py-3 px-4 min-w-[150px] w-[150px] text-center whitespace-normal break-all overflow-hidden">
                            {doc.documentExpire
                              ? new Date(doc.documentExpire).toLocaleDateString()
                              : 'N/A'
                            }
                          </td>
                          <td className="py-3 px-4 min-w-[120px] w-[120px] text-center whitespace-normal break-all overflow-hidden">
                            {(() => {
                              const status = getDocumentStatus(doc);
                              return (
                                <span className={`px-2 py-1 text-xs rounded-full ${
                                  status === 'verified'
                                    ? 'bg-green-100 text-green-800'
                                    : status === 'rejected'
                                    ? 'bg-red-100 text-red-800'
                                    : 'bg-yellow-100 text-yellow-800'
                                }`}>
                                  {status === 'verified'
                                    ? 'Verified'
                                    : status === 'rejected'
                                    ? 'Rejected'
                                    : 'Pending'
                                  }
                                </span>
                              );
                            })()}
                          </td>
                          <td className="py-3 px-4 min-w-[120px] w-[120px] whitespace-normal break-all overflow-hidden text-center">
                            <div className="flex gap-4 justify-center">
                              <button
                                onClick={() => handleView(doc._id)}
                                className="transition-colors p-1 hover:bg-blue-50 rounded"
                                title="View Details"
                              >
                                <svg
                                  width="20"
                                  height="20"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  className="text-blue-600"
                                >
                                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
                                  <circle cx="12" cy="12" r="3" />
                                </svg>
                              </button>
                              <button
                                onClick={() => handleVerify(doc._id)}
                                className="transition-colors p-1 hover:bg-green-50 rounded"
                                title="Verify Document"
                              >
                                <svg
                                  width="20"
                                  height="20"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  className="text-green-600"
                                >
                                  <path d="M9 12l2 2 4-4" />
                                  <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3" />
                                  <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3" />
                                  <path d="M12 21c0-1-1-3-3-3s-3 2-3 3 1 3 3 3 3-2 3-3" />
                                  <path d="M12 3c0 1-1 3-3 3s-3-2-3-3 1-3 3-3 3 2 3 3" />
                                </svg>
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>


          </div>
        </div>
      </div>

      {/* Bulk Verify Document Modal */}
      {isBulkVerifyModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-7xl max-h-[95vh] overflow-y-auto shadow-2xl">
            {/* Modal Header */}
            <div className="flex justify-between items-center p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">Verify Documents</h2>
              <button
                onClick={closeBulkVerifyModal}
                className="text-gray-400 hover:text-gray-600 transition-colors p-1"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Filter Section */}
            <div className="bg-gray-50 p-4 border-b border-gray-200">
              <div className="flex flex-wrap gap-4">
                {/* Entity Filter */}
                <div className="flex flex-col">
                  <label className="text-sm font-medium text-gray-700 mb-2">Select Entity</label>
                  <select
                    value={bulkSelectedEntity}
                    onChange={(e) => setBulkSelectedEntity(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white min-w-[140px]"
                  >
                    <option value="all">All Entities</option>
                    <option value="driver">Driver</option>
                    <option value="vehicle">Vehicle</option>
                  </select>
                </div>

                {/* Document Type Filter */}
                <div className="flex flex-col">
                  <label className="text-sm font-medium text-gray-700 mb-2">Document Type</label>
                  <select
                    value={bulkSelectedDocumentType}
                    onChange={(e) => setBulkSelectedDocumentType(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white min-w-[140px]"
                  >
                    <option value="all">All Types</option>
                    {getUniqueDocumentTypes().map(type => (
                      <option key={type} value={type}>
                        {type.charAt(0).toUpperCase() + type.slice(1)}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Status Filter */}
                <div className="flex flex-col">
                  <label className="text-sm font-medium text-gray-700 mb-2">Status</label>
                  <select
                    value={bulkSelectedStatus}
                    onChange={(e) => setBulkSelectedStatus(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white min-w-[140px]"
                  >
                    <option value="all">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="verified">Verified</option>
                    <option value="rejected">Rejected</option>
                  </select>
                </div>

                {/* Clear Filters Button */}
                <div className="flex flex-col justify-end">
                  <button
                    onClick={() => {
                      setBulkSelectedEntity("all");
                      setBulkSelectedDocumentType("all");
                      setBulkSelectedStatus("all");
                    }}
                    className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 font-medium"
                  >
                    Clear Filters
                  </button>
                </div>
              </div>
            </div>

            {/* Documents Table */}
            <div className="p-6">
              <div className="mb-4 flex justify-between items-center">
                <p className="text-sm text-gray-600">
                  Showing {getBulkFilteredDocuments().length} documents
                </p>

                {/* Bulk Actions */}
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => {
                      const filteredDocs = getBulkFilteredDocuments();
                      const newStates = { ...documentVerificationStates };
                      filteredDocs.forEach(doc => {
                        newStates[doc._id] = 'verified';
                      });
                      setDocumentVerificationStates(newStates);
                    }}
                    className="px-3 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"
                  >
                    Verify All Visible
                  </button>
                  <button
                    onClick={() => {
                      const filteredDocs = getBulkFilteredDocuments();
                      const newStates = { ...documentVerificationStates };
                      filteredDocs.forEach(doc => {
                        newStates[doc._id] = 'rejected';
                      });
                      setDocumentVerificationStates(newStates);
                    }}
                    className="px-3 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
                  >
                    Reject All Visible
                  </button>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full bg-white border">
                  <thead className="font-sans font-bold text-sm text-center">
                    <tr className="text-white bg-[#38384A]">
                      <th className="py-3 px-4 text-left">Document Type</th>
                      <th className="py-3 px-4 text-left">Uploaded On</th>
                      <th className="py-3 px-4 text-left">Expiry Date</th>
                      <th className="py-3 px-4 text-left">Status</th>
                      <th className="py-3 px-4 text-left">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="font-sans font-medium text-sm">
                    {getBulkFilteredDocuments().length === 0 ? (
                      <tr>
                        <td colSpan="5" className="py-8 text-center text-gray-500">
                          No documents found matching the selected filters
                        </td>
                      </tr>
                    ) : (
                      getBulkFilteredDocuments().map((doc, index) => (
                        <tr key={doc._id || index} className="border-b">
                          <td className="py-3 px-4">
                            <div className="flex flex-col">
                              <span className="font-medium">
                                {doc.documentname ? doc.documentname.charAt(0).toUpperCase() + doc.documentname.slice(1) : 'Document'}
                              </span>
                              <span className="text-xs text-gray-500">
                                {doc.documentType === 'driver' || doc.Driverid ? 'Driver' : 'Vehicle'} - {getEntityDisplayName(doc)}
                              </span>
                            </div>
                          </td>
                          <td className="py-3 px-4 text-gray-700">
                            {doc.createdAt ? formatDate(doc.createdAt) : 'N/A'}
                          </td>
                          <td className="py-3 px-4 text-gray-700">
                            {doc.documentExpire ? formatDate(doc.documentExpire) : 'N/A'}
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex flex-col space-y-2">
                              {/* Current Status Display with Date-based "New" status */}
                              <div className="flex items-center mb-2">
                                <span className="text-xs text-gray-500 mr-2">Current:</span>
                                {(() => {
                                  const status = getDisplayStatus(doc, true); // Use modal context
                                  return (
                                    <span className={`px-2 py-1 text-xs rounded-full ${
                                      status === 'verified'
                                        ? 'bg-green-100 text-green-800'
                                        : status === 'rejected'
                                        ? 'bg-red-100 text-red-800'
                                        : status === 'new'
                                        ? 'bg-blue-100 text-blue-800'
                                        : 'bg-yellow-100 text-yellow-800'
                                    }`}>
                                      {status === 'verified'
                                        ? 'Verified'
                                        : status === 'rejected'
                                        ? 'Rejected'
                                        : status === 'new'
                                        ? 'New'
                                        : 'Pending'
                                      }
                                    </span>
                                  );
                                })()}
                              </div>

                              {/* Verification Options */}
                              <div className="flex items-center space-x-4">
                                <label className="flex items-center cursor-pointer">
                                  <input
                                    type="radio"
                                    name={`verification_${doc._id}`}
                                    value="verified"
                                    checked={documentVerificationStates[doc._id] === "verified"}
                                    onChange={(e) => handleDocumentVerificationChange(doc._id, e.target.value)}
                                    className="w-4 h-4 text-green-600 border-gray-300 focus:ring-green-500"
                                  />
                                  <span className="ml-2 text-sm text-gray-700">Verified</span>
                                </label>
                                <label className="flex items-center cursor-pointer">
                                  <input
                                    type="radio"
                                    name={`verification_${doc._id}`}
                                    value="rejected"
                                    checked={documentVerificationStates[doc._id] === "rejected"}
                                    onChange={(e) => handleDocumentVerificationChange(doc._id, e.target.value)}
                                    className="w-4 h-4 text-red-600 border-gray-300 focus:ring-red-500"
                                  />
                                  <span className="ml-2 text-sm text-gray-700">Rejected</span>
                                </label>
                              </div>
                            </div>
                          </td>
                          <td className="py-3 px-4">
                            {/* Check if document is in inline verification mode */}
                            {inlineVerificationStates[doc._id] ? (
                              /* Inline Verification Controls */
                              <div className="flex flex-col space-y-3 p-3 bg-gray-50 rounded-lg border">
                                <div className="text-sm font-medium text-gray-700">Verify Document:</div>

                                {/* Verification Radio Buttons */}
                                <div className="flex items-center space-x-4">
                                  <label className="flex items-center cursor-pointer">
                                    <input
                                      type="radio"
                                      name={`inline_verification_${doc._id}`}
                                      value="verified"
                                      checked={inlineVerificationValues[doc._id] === "verified"}
                                      onChange={(e) => handleInlineVerificationChange(doc._id, e.target.value)}
                                      className="w-4 h-4 text-green-600 border-gray-300 focus:ring-green-500"
                                    />
                                    <span className="ml-2 text-sm text-gray-700">Verified</span>
                                  </label>
                                  <label className="flex items-center cursor-pointer">
                                    <input
                                      type="radio"
                                      name={`inline_verification_${doc._id}`}
                                      value="rejected"
                                      checked={inlineVerificationValues[doc._id] === "rejected"}
                                      onChange={(e) => handleInlineVerificationChange(doc._id, e.target.value)}
                                      className="w-4 h-4 text-red-600 border-gray-300 focus:ring-red-500"
                                    />
                                    <span className="ml-2 text-sm text-gray-700">Rejected</span>
                                  </label>
                                </div>

                                {/* Submit and Cancel Buttons */}
                                <div className="flex items-center space-x-2">
                                  <button
                                    onClick={() => handleInlineVerificationSubmit(doc._id)}
                                    className="px-3 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
                                  >
                                    Submit
                                  </button>
                                  <button
                                    onClick={() => handleInlineVerificationCancel(doc._id)}
                                    className="px-3 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                                  >
                                    Cancel
                                  </button>
                                </div>
                              </div>
                            ) : (
                              /* Normal Action Buttons */
                              <div className="flex flex-col space-y-2">
                                {/* View Document */}
                                {doc.document && (
                                  <button
                                    onClick={() => window.open(getDocumentUrl(doc.document), '_blank')}
                                    className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
                                  >
                                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 616 0z" />
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                    View
                                  </button>
                                )}

                                {/* Verify Button - Only show for documents that can be verified */}
                                {(() => {
                                  const currentStatus = getDisplayStatus(doc, true);
                                  const canVerify = currentStatus !== 'verified'; // Don't show verify button for already verified documents

                                  return canVerify && (
                                    <button
                                      onClick={() => handleInlineVerifyToggle(doc._id)}
                                      className="text-green-600 hover:text-green-800 text-sm flex items-center"
                                    >
                                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                      </svg>
                                      Verify
                                    </button>
                                  );
                                })()}

                                {/* Notify Button */}
                                <button
                                  onClick={() => toast.info(`Notification sent for ${doc.documentname || 'document'}`)}
                                  className="text-orange-600 hover:text-orange-800 text-sm flex items-center"
                                >
                                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM4.868 19.718A10.97 10.97 0 0112 22a10.97 10.97 0 017.132-2.282M4.868 19.718A10.97 10.97 0 014 12a8 8 0 1116 0 10.97 10.97 0 01-.868 7.718" />
                                  </svg>
                                  Notify
                                </button>
                              </div>
                            )}
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Changes Summary */}
            <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
              <div className="text-sm text-gray-600">
                {(() => {
                  const changedCount = Object.keys(documentVerificationStates).filter(docId => {
                    const doc = documents.find(d => d._id === docId);
                    const newStatus = documentVerificationStates[docId];
                    const currentStatus = getDocumentStatus(doc);
                    return newStatus !== currentStatus;
                  }).length;

                  return changedCount > 0
                    ? `${changedCount} document${changedCount > 1 ? 's' : ''} will be updated`
                    : 'No changes to save';
                })()}
              </div>
            </div>

            {/* Modal Actions */}
            <div className="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
              <button
                onClick={closeBulkVerifyModal}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
              >
                Cancel
              </button>
              <button
                onClick={handleBulkVerificationSubmit}
                className="px-4 py-2 bg-gray-800 text-white rounded-md hover:bg-gray-900 transition-colors text-sm font-medium"
              >
                Submit Changes
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Verify Document Modal */}
      {isVerifyModalOpen && selectedDocument && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-5xl max-h-[95vh] overflow-y-auto shadow-2xl">
            {/* Modal Header */}
            <div className="flex justify-between items-center p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">Verify Document</h2>
              <button
                onClick={closeVerifyModal}
                className="text-gray-400 hover:text-gray-600 transition-colors p-1"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            {/* Vehicle/Driver Info */}
            <div className="px-6 py-4 bg-gray-50">
              <p className="text-base text-gray-700">
                <span className="text-gray-900">
                  {getEntityDisplayName(selectedDocument)}
                </span>
              </p>
            </div>

            {/* Document Table */}
            <div className="px-6 py-4">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-0 font-medium text-gray-600 text-sm">Document Type</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600 text-sm">Uploaded On</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600 text-sm">Expiry Date</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600 text-sm">Status</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600 text-sm">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {/* Current Document - Highlighted */}
                  <tr className="border-b border-gray-100 bg-blue-50">
                    <td className="py-3 px-0 text-gray-700 text-sm font-medium">
                      {selectedDocument.documentname ? selectedDocument.documentname.charAt(0).toUpperCase() + selectedDocument.documentname.slice(1) : 'Document'}
                    </td>
                    <td className="py-3 px-4 text-gray-700 text-sm">
                      {selectedDocument.createdAt ? formatDate(selectedDocument.createdAt) : 'N/A'}
                    </td>
                    <td className="py-3 px-4 text-gray-700 text-sm">
                      {selectedDocument.documentExpire ? formatDate(selectedDocument.documentExpire) : 'N/A'}
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center">
                        {(() => {
                          const status = getDisplayStatus(selectedDocument, true); // Use modal context for date-based "New" status
                          return (
                            <>
                              <div className={`w-2 h-2 rounded-full mr-2 ${
                                status === 'verified'
                                  ? 'bg-green-500'
                                  : status === 'rejected'
                                  ? 'bg-red-500'
                                  : status === 'new'
                                  ? 'bg-blue-500'
                                  : 'bg-yellow-500'
                              }`}></div>
                              <span className="text-sm text-gray-700">
                                {status === 'verified'
                                  ? 'Verified'
                                  : status === 'rejected'
                                  ? 'Rejected'
                                  : status === 'new'
                                  ? 'New'
                                  : 'Pending'
                                }
                              </span>
                            </>
                          );
                        })()}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center space-x-2">
                        {selectedDocument.document && (
                          <button
                            onClick={() => window.open(getDocumentUrl(selectedDocument.document), '_blank')}
                            className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
                          >
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 616 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                            View
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>

                  {/* Driver Information Rows (if available) */}
                  {selectedDocument.Driverid && (
                    <>
                      {/* License Information */}
                      {selectedDocument.Driverid.licenseNumber && (
                        <tr className="border-b border-gray-100">
                          <td className="py-3 px-0 text-gray-700 text-sm">License</td>
                          <td className="py-3 px-4 text-gray-700 text-sm">
                            {selectedDocument.Driverid.createdAt ? formatDate(selectedDocument.Driverid.createdAt) : 'N/A'}
                          </td>
                          <td className="py-3 px-4 text-gray-700 text-sm">
                            {selectedDocument.Driverid.licenseExpiryDate ? formatDate(selectedDocument.Driverid.licenseExpiryDate) : 'N/A'}
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex items-center">
                              <div className={`w-2 h-2 rounded-full mr-2 ${
                                selectedDocument.Driverid.licenseExpiryDate && new Date(selectedDocument.Driverid.licenseExpiryDate) > new Date()
                                  ? 'bg-green-500'
                                  : 'bg-red-500'
                              }`}></div>
                              <span className="text-sm text-gray-700">
                                {selectedDocument.Driverid.licenseExpiryDate && new Date(selectedDocument.Driverid.licenseExpiryDate) > new Date()
                                  ? 'Valid'
                                  : 'Expired'
                                }
                              </span>
                            </div>
                          </td>
                          <td className="py-3 px-4">
                            <span className="text-sm text-gray-500">License: {selectedDocument.Driverid.licenseNumber}</span>
                          </td>
                        </tr>
                      )}

                      {/* Taxi Badge Information */}
                      {selectedDocument.Driverid.taxiBadgeDate && (
                        <tr className="border-b border-gray-100">
                          <td className="py-3 px-0 text-gray-700 text-sm">Taxi Badge</td>
                          <td className="py-3 px-4 text-gray-700 text-sm">
                            {formatDate(selectedDocument.Driverid.taxiBadgeDate)}
                          </td>
                          <td className="py-3 px-4 text-gray-700 text-sm">N/A</td>
                          <td className="py-3 px-4">
                            <div className="flex items-center">
                              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                              <span className="text-sm text-gray-700">Valid</span>
                            </div>
                          </td>
                          <td className="py-3 px-4">
                            <span className="text-sm text-gray-500">Badge Date</span>
                          </td>
                        </tr>
                      )}

                      {/* Insurance Information */}
                      {selectedDocument.Driverid.insurance && (
                        <tr className="border-b border-gray-100">
                          <td className="py-3 px-0 text-gray-700 text-sm">Insurance</td>
                          <td className="py-3 px-4 text-gray-700 text-sm">N/A</td>
                          <td className="py-3 px-4 text-gray-700 text-sm">N/A</td>
                          <td className="py-3 px-4">
                            <div className="flex items-center">
                              <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                              <span className="text-sm text-gray-700">Pending</span>
                            </div>
                          </td>
                          <td className="py-3 px-4">
                            <span className="text-sm text-gray-500">{selectedDocument.Driverid.insurance}</span>
                          </td>
                        </tr>
                      )}
                    </>
                  )}

                  {/* Vehicle Information Rows (if available) */}
                  {selectedDocument.Vehicleid && (
                    <>
                      {/* MOT Information */}
                      {selectedDocument.Vehicleid.motDueDate && (
                        <tr className="border-b border-gray-100">
                          <td className="py-3 px-0 text-gray-700 text-sm">MOT</td>
                          <td className="py-3 px-4 text-gray-700 text-sm">
                            {selectedDocument.Vehicleid.createdAt ? formatDate(selectedDocument.Vehicleid.createdAt) : 'N/A'}
                          </td>
                          <td className="py-3 px-4 text-gray-700 text-sm">
                            {formatDate(selectedDocument.Vehicleid.motDueDate)}
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex items-center">
                              <div className={`w-2 h-2 rounded-full mr-2 ${
                                new Date(selectedDocument.Vehicleid.motDueDate) > new Date()
                                  ? 'bg-green-500'
                                  : 'bg-red-500'
                              }`}></div>
                              <span className="text-sm text-gray-700">
                                {new Date(selectedDocument.Vehicleid.motDueDate) > new Date()
                                  ? 'Valid'
                                  : 'Expired'
                                }
                              </span>
                            </div>
                          </td>
                          <td className="py-3 px-4">
                            <span className="text-sm text-gray-500">MOT Due Date</span>
                          </td>
                        </tr>
                      )}

                      {/* Registration Information */}
                      {selectedDocument.Vehicleid.registrationNumber && (
                        <tr className="border-b border-gray-100">
                          <td className="py-3 px-0 text-gray-700 text-sm">Registration</td>
                          <td className="py-3 px-4 text-gray-700 text-sm">
                            {selectedDocument.Vehicleid.createdAt ? formatDate(selectedDocument.Vehicleid.createdAt) : 'N/A'}
                          </td>
                          <td className="py-3 px-4 text-gray-700 text-sm">
                            {selectedDocument.Vehicleid.PlateExpiryDate ? formatDate(selectedDocument.Vehicleid.PlateExpiryDate) : 'N/A'}
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex items-center">
                              <div className={`w-2 h-2 rounded-full mr-2 ${
                                selectedDocument.Vehicleid.PlateExpiryDate && new Date(selectedDocument.Vehicleid.PlateExpiryDate) > new Date()
                                  ? 'bg-green-500'
                                  : 'bg-yellow-500'
                              }`}></div>
                              <span className="text-sm text-gray-700">
                                {selectedDocument.Vehicleid.PlateExpiryDate && new Date(selectedDocument.Vehicleid.PlateExpiryDate) > new Date()
                                  ? 'Valid'
                                  : 'Check Required'
                                }
                              </span>
                            </div>
                          </td>
                          <td className="py-3 px-4">
                            <span className="text-sm text-gray-500">Reg: {selectedDocument.Vehicleid.registrationNumber}</span>
                          </td>
                        </tr>
                      )}

                      {/* Insurance Information */}
                      {selectedDocument.Vehicleid.Insurance && (
                        <tr className="border-b border-gray-100">
                          <td className="py-3 px-0 text-gray-700 text-sm">Vehicle Insurance</td>
                          <td className="py-3 px-4 text-gray-700 text-sm">N/A</td>
                          <td className="py-3 px-4 text-gray-700 text-sm">N/A</td>
                          <td className="py-3 px-4">
                            <div className="flex items-center">
                              <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                              <span className="text-sm text-gray-700">Pending</span>
                            </div>
                          </td>
                          <td className="py-3 px-4">
                            <span className="text-sm text-gray-500">{selectedDocument.Vehicleid.Insurance}</span>
                          </td>
                        </tr>
                      )}
                    </>
                  )}

                  {/* Show message if no additional information is available */}
                  {(!selectedDocument.Driverid && !selectedDocument.Vehicleid) ||
                   (selectedDocument.Driverid && !selectedDocument.Driverid.licenseNumber && !selectedDocument.Driverid.taxiBadgeDate && !selectedDocument.Driverid.insurance) ||
                   (selectedDocument.Vehicleid && !selectedDocument.Vehicleid.motDueDate && !selectedDocument.Vehicleid.registrationNumber && !selectedDocument.Vehicleid.Insurance) ? (
                    <tr className="border-b border-gray-100">
                      <td colSpan="5" className="py-6 text-center text-gray-500 text-sm">
                        No additional document information available
                      </td>
                    </tr>
                  ) : null}
                </tbody>
              </table>
            </div>

            {/* Document Preview and Verification Section */}
            <div className="px-6 py-4 flex gap-8">
              {/* Document Preview */}
              <div className="flex-shrink-0">
                {selectedDocument.document ? (
                  <div className="w-40 h-48 border border-gray-300 rounded-lg overflow-hidden bg-gray-50">
                    {selectedDocument.document.toLowerCase().includes('.pdf') ? (
                      // PDF Preview
                      <div className="w-full h-full flex flex-col items-center justify-center">
                        <div className="w-24 h-32 border border-gray-200 rounded bg-white flex flex-col items-center justify-center mb-2">
                          <div className="text-lg font-bold text-red-600 mb-1">PDF</div>
                          <div className="w-16 h-1 bg-gray-300 rounded mb-1"></div>
                          <div className="w-12 h-1 bg-gray-300 rounded mb-1"></div>
                          <div className="w-14 h-1 bg-gray-300 rounded"></div>
                        </div>
                        <button
                          onClick={() => window.open(getDocumentUrl(selectedDocument.document), '_blank')}
                          className="text-xs text-blue-600 hover:text-blue-800 mt-1"
                        >
                          View Full Document
                        </button>
                      </div>
                    ) : (
                      // Image Preview
                      <div className="relative w-full h-full">
                        <img
                          src={getDocumentUrl(selectedDocument.document)}
                          alt="Document preview"
                          className="w-full h-full object-cover cursor-pointer"
                          onClick={() => window.open(getDocumentUrl(selectedDocument.document), '_blank')}
                          onError={(e) => {
                            e.target.style.display = 'none';
                            e.target.nextSibling.style.display = 'flex';
                          }}
                        />
                        <div className="w-full h-full flex flex-col items-center justify-center" style={{display: 'none'}}>
                          <div className="text-sm text-gray-500 mb-2">Document Preview</div>
                          <div className="text-xs text-gray-400">Click to view full document</div>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (

                  <div className="w-40 h-48 border border-gray-300 rounded-lg bg-gray-50 flex flex-col items-center justify-center">
                    <div className="w-24 h-32 border border-gray-200 rounded bg-white flex flex-col items-center justify-center mb-2">
                      <div className="text-lg font-bold text-gray-400 mb-1">N/A</div>
                      <div className="w-16 h-1 bg-gray-200 rounded mb-1"></div>
                      <div className="w-12 h-1 bg-gray-200 rounded mb-1"></div>
                      <div className="w-14 h-1 bg-gray-200 rounded"></div>
                    </div>
                    <div className="text-xs text-gray-400">No Document Available</div>
                  </div>
                )}
              </div>

              {/* Verification Options */}
              <div className="flex-1 flex items-center">
                <div className="flex items-center space-x-8">
                  <label className="flex items-center cursor-pointer">
                    <input
                      type="radio"
                      name="verificationStatus"
                      value="verified"
                      checked={verificationStatus === "verified"}
                      onChange={(e) => setVerificationStatus(e.target.value)}
                      className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <span className="ml-3 text-sm text-gray-700">Verified</span>
                  </label>
                  <label className="flex items-center cursor-pointer">
                    <input
                      type="radio"
                      name="verificationStatus"
                      value="rejected"
                      checked={verificationStatus === "rejected"}
                      onChange={(e) => setVerificationStatus(e.target.value)}
                      className="w-4 h-4 text-red-600 border-gray-300 focus:ring-red-500"
                    />
                    <span className="ml-3 text-sm text-gray-700">Rejected</span>
                  </label>
                </div>
              </div>
            </div>

            {/* Modal Actions */}
            <div className="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
              <button
                onClick={closeVerifyModal}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
              >
                Cancel
              </button>
              <button
                onClick={handleVerifySubmit}
                className="px-4 py-2 bg-gray-800 text-white rounded-md hover:bg-gray-900 transition-colors text-sm font-medium"
              >
                Submit
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentReportPage;
